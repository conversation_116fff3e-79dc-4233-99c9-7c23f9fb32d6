/// -----
/// safety_exam_page.dart
/// 
/// 安全教育考试路由页面
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/safety/di/safety_injection.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/safety_exam_bloc.dart';
import 'package:flutter_demo/features/safety/presentation/screens/safety_exam_screen.dart';

/// 安全教育考试路由页面
///
/// 提供BLoC提供者，用于路由系统
class SafetyExamPage extends StatelessWidget {
  /// 构造函数
  const SafetyExamPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => getIt<SafetyExamBloc>(),
      child: const SafetyExamScreen(),
    );
  }
}
