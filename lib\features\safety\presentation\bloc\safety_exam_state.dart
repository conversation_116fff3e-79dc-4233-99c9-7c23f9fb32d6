/// -----
/// safety_exam_state.dart
/// 
/// 安全教育考试状态类
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';

/// 安全教育考试状态基类
///
/// 所有安全教育考试相关状态的基类
abstract class SafetyExamState extends Equatable {
  /// 构造函数
  const SafetyExamState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
///
/// 安全教育考试初始状态
class SafetyExamInitial extends SafetyExamState {}

/// 加载中状态
///
/// 正在加载安全教育考试题目
class SafetyExamLoading extends SafetyExamState {}

/// 加载失败状态
///
/// 加载安全教育考试题目失败
class SafetyExamLoadFailure extends SafetyExamState {
  /// 错误信息
  final String message;

  /// 构造函数
  const SafetyExamLoadFailure({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

/// 答题中状态
///
/// 正在进行安全教育考试答题
class SafetyExamInProgress extends SafetyExamState {
  /// 所有题目列表
  final List<SafetyExamQuestion> questions;
  
  /// 当前题目索引
  final int currentQuestionIndex;
  
  /// 用户选择的答案映射（键为题目ID，值为选择的选项ID列表，支持多选）
  final Map<String, List<String>> userAnswers;

  /// 当前选择的选项ID列表
  final List<String>? selectedOptionIds;
  
  /// 正确题目数量
  final int correctCount;
  
  /// 错误题目数量
  final int incorrectCount;

  /// 构造函数
  const SafetyExamInProgress({
    required this.questions,
    required this.currentQuestionIndex,
    required this.userAnswers,
    this.selectedOptionIds,
    required this.correctCount,
    required this.incorrectCount,
  });

  @override
  List<Object?> get props => [
    questions,
    currentQuestionIndex,
    userAnswers,
    selectedOptionIds,
    correctCount,
    incorrectCount,
  ];

  /// 获取当前题目
  SafetyExamQuestion get currentQuestion => questions[currentQuestionIndex];

  /// 判断是否为最后一题
  bool get isLastQuestion => currentQuestionIndex == questions.length - 1;

  /// 判断当前题目是否已回答
  bool get isCurrentQuestionAnswered => 
      userAnswers.containsKey(currentQuestion.id);

  /// 判断当前选择的答案是否正确
  bool get isCurrentAnswerCorrect {
    final currentAnswers = userAnswers[currentQuestion.id];
    if (currentAnswers == null || currentAnswers.isEmpty) {
      return false;
    }

    // 检查用户答案是否与正确答案完全匹配
    final correctAnswers = currentQuestion.correctAnswers;
    if (currentAnswers.length != correctAnswers.length) {
      return false;
    }

    for (final answer in correctAnswers) {
      if (!currentAnswers.contains(answer)) {
        return false;
      }
    }

    return true;
  }

  /// 创建新状态
  SafetyExamInProgress copyWith({
    List<SafetyExamQuestion>? questions,
    int? currentQuestionIndex,
    Map<String, List<String>>? userAnswers,
    List<String>? selectedOptionIds,
    int? correctCount,
    int? incorrectCount,
  }) {
    return SafetyExamInProgress(
      questions: questions ?? this.questions,
      currentQuestionIndex: currentQuestionIndex ?? this.currentQuestionIndex,
      userAnswers: userAnswers ?? this.userAnswers,
      selectedOptionIds: selectedOptionIds,
      correctCount: correctCount ?? this.correctCount,
      incorrectCount: incorrectCount ?? this.incorrectCount,
    );
  }
}

/// 考试完成状态
///
/// 安全教育考试已完成，显示结果
class SafetyExamCompleted extends SafetyExamState {
  /// 所有题目列表
  final List<SafetyExamQuestion> questions;

  /// 用户选择的答案映射（键为题目ID，值为选择的选项ID列表）
  final Map<String, List<String>> userAnswers;

  /// 正确题目数量
  final int correctCount;

  /// 错误题目数量
  final int incorrectCount;

  /// 构造函数
  const SafetyExamCompleted({
    required this.questions,
    required this.userAnswers,
    required this.correctCount,
    required this.incorrectCount,
  });

  @override
  List<Object?> get props => [
    questions,
    userAnswers,
    correctCount,
    incorrectCount,
  ];

  /// 计算得分百分比
  double get scorePercentage =>
      questions.isEmpty ? 0 : (correctCount / questions.length) * 100;
}
