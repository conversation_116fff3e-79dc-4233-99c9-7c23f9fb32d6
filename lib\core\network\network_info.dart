/// -----
/// network_info.dart
///
/// 网络信息工具类，用于检查网络连接状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:internet_connection_checker/internet_connection_checker.dart';

/// 网络信息工具类
///
/// 用于检查网络连接状态
abstract class NetworkInfo {
  /// 检查是否有网络连接
  Future<bool> get isConnected;
}

/// 网络信息实现类
class NetworkInfoImpl implements NetworkInfo {
  final InternetConnectionChecker connectionChecker;

  NetworkInfoImpl(this.connectionChecker);

  @override
  Future<bool> get isConnected => connectionChecker.hasConnection;
} 