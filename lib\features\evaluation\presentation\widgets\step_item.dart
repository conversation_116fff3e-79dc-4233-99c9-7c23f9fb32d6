/// -----
/// step_item.dart
///
/// 企业评分流程步骤项组件，用于显示单个步骤的图标和描述文字
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 步骤项组件
///
/// 用于显示企业评分流程中的单个步骤
/// 包含步骤图标和描述文字
class StepItem extends StatelessWidget {
  /// 步骤图标路径
  final String iconPath;

  /// 步骤描述文字
  final String description;

  /// 步骤编号
  final String stepNumber;

  const StepItem({
    Key? key,
    required this.iconPath,
    required this.description,
    required this.stepNumber,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 步骤图标
        Container(
          width: 60.w,
          height: 60.w,
          child: Image.asset(
            iconPath,
            width: 60.w,
            height: 60.w,
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              // 如果图标加载失败，显示数字
              return Container(
                width: 60.w,
                height: 60.w,
                decoration: BoxDecoration(
                  color: const Color(0xFF3E6BFD),
                  borderRadius: BorderRadius.circular(8.r),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF3E6BFD).withOpacity(0.3),
                      blurRadius: 8.r,
                      offset: Offset(0, 4.h),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    stepNumber,
                    style: TextStyle(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              );
            },
          ),
        ),

        SizedBox(width: 24.w),

        // 步骤描述
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(top: 8.h),
            child: Text(
              description,
              style: TextStyle(
                fontSize: 28.sp,
                color: const Color(0xFF333333),
                height: 1.4,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
