import requests
import json  # 用于美化 JSON 输出

url = "http://**************:8088/internshipservice/v1/internship/teacher/examQuestion/list"
params = {
    "planId": 8,
}
headers = {
    "token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMDI1MDUyNyIsImRlcHQiOjE5MjcyNDgzOTY4OTE5NjMzOTMsInVzZXJUeXBlIjoyLCJ1c2VyS2V5IjoiMDIwNjdENjc0QkVDMTFGMDgyREMwMjQyQzBBODY0MDcifQ.Y2nnKkKolxxuUsWoiOFeBGAhJr-t-v3KHJ3ZcNtWgDqK6LXqDbu3UfSvGPrIi4dGTjVqv5CymOPHFwdJsSJn2g" 
}

try:
    response = requests.get(url, params=params, headers=headers)
    response.raise_for_status()  # 检查请求是否成功
    
    # 获取 JSON 数据并美化输出
    json_data = response.json()
    pretty_json = json.dumps(json_data, indent=4, ensure_ascii=False)  # 缩进4个空格，支持中文
    
    print("请求成功！")
    print("状态码:", response.status_code)
    print("美化后的 JSON 响应:")
    print(pretty_json)
    
except requests.exceptions.RequestException as e:
    print("请求失败:", e)