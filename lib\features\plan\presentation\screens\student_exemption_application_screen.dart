/// -----
/// student_exemption_application_screen.dart
/// 
/// 学生免实习申请页面，允许学生提交免实习申请及上传相关证明文件
///
/// <AUTHOR>
/// @date 2025-05-27
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

/// 学生免实习申请页面
///
/// 学生可以在此页面选择实习学期、填写免实习原因、上传证明文件并提交申请
/// 支持上传1个证明文件
class StudentExemptionApplicationScreen extends StatefulWidget {
  const StudentExemptionApplicationScreen({Key? key}) : super(key: key);

  @override
  State<StudentExemptionApplicationScreen> createState() => _StudentExemptionApplicationScreenState();
}

class _StudentExemptionApplicationScreenState extends State<StudentExemptionApplicationScreen> {
  // 选择的学期信息
  String _selectedSemester = '2021级市场销售2023-2024实习学年第二学期岗位实习';
  
  // 学生去向，默认为"参军"
  String _studentDirection = '参军';
  
  // 证明文件
  File? _proofDocument;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: const CustomAppBar(
        title: '免实习申请',
        backgroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 内容区域（可滚动）
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 学期选择
                  CourseHeaderSection(courseName: _selectedSemester),

                  SizedBox(height: 21.h),

                  // 免实习申请信息
                  _buildApplicationInfo(),

                  SizedBox(height: 10.h),

                  // 证明文件上传
                  _buildDocumentUpload(),

                  SizedBox(height: 30.h),
                ],
              ),
            ),
          ),
          
          // 提交按钮（固定在底部）
          _buildSubmitButton(),
        ],
      ),
    );
  }

  // 学期选择器
  Widget _buildSemesterSelector() {
    return Container(
      color: Colors.white,
      child: ListTile(
        title: Text(
          _selectedSemester,
          style: const TextStyle(
            color: Colors.black87,
            fontSize: 16,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_drop_down,
          size: 24,
          color: Colors.grey,
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 20),
        onTap: _showSemesterDialog,
      ),
    );
  }

  // 免实习申请信息
  Widget _buildApplicationInfo() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '免实习申请信息',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
          SizedBox(height: 28.h),
          Row(
            children: [
              Text(
                '学生去向:',
                style: TextStyle(
                  fontSize: 28.sp,
                  color: AppTheme.black333,
                ),
              ),
              const SizedBox(width: 10),
              Text(
                _studentDirection,
                style: const TextStyle(
                  fontSize: 15,
                  color: AppTheme.black333,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 证明文件上传区域
  Widget _buildDocumentUpload() {
    return Container(
      width: double.infinity,
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 40.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '证明文件上传',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
          const SizedBox(height: 16),
          _proofDocument == null
              ? _buildAddDocumentButton()
              : _buildDocumentPreview(),
        ],
      ),
    );
  }

  // 添加文件按钮
  Widget _buildAddDocumentButton() {
    return GestureDetector(
      onTap: _pickDocument,
      child: Container(
        width: 100,
        height: 100,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Icon(
            Icons.add,
            size: 40,
            color: Colors.grey,
          ),
        ),
      ),
    );
  }

  // 文件预览
  Widget _buildDocumentPreview() {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
            image: DecorationImage(
              image: FileImage(_proofDocument!),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: -10,
          right: -10,
          child: GestureDetector(
            onTap: () {
              setState(() {
                _proofDocument = null;
              });
            },
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 提交按钮
  Widget _buildSubmitButton() {
    return Container(
      width: double.infinity,
      height: 128.h,
      color: Colors.white,
      // padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 16.h),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 25.w,vertical: 20.h),
        height: 88.h,
        child: ElevatedButton(
          onPressed: _submitApplication,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.r),
            ),
            elevation: 0,
          ),
          child: const Text(
            '提交',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  // 显示学期选择对话框
  void _showSemesterDialog() {
    // 这里可以实现学期选择的对话框，暂时保持当前选择不变
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('此功能暂未实现，保持当前学期选择')),
    );
  }

  // 选择证明文件
  Future<void> _pickDocument() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        setState(() {
          _proofDocument = File(pickedFile.path);
        });
      }
    } catch (e) {
      debugPrint('Error picking document: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('选择文件失败，请重试')),
      );
    }
  }

  // 提交申请
  void _submitApplication() {
    // 检查是否上传了证明文件
    if (_proofDocument == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请上传证明文件')),
      );
      return;
    }

    // 这里可以添加提交申请的逻辑，如发送到服务器等

    // 显示成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('申请提交成功，请等待审核！')),
    );

    // 提交成功后返回上一页
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        Navigator.pop(context);
      }
    });
  }
}
