/// -----
/// safety_exam_bloc.dart
/// 
/// 安全教育考试BLoC类
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_demo/features/safety/domain/usecases/get_exam_questions_usecase.dart';
import 'package:flutter_demo/features/safety/domain/utils/exam_score_calculator.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/safety_exam_event.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/safety_exam_state.dart';

/// 安全教育考试BLoC
///
/// 处理安全教育考试相关的业务逻辑
class SafetyExamBloc extends Bloc<SafetyExamEvent, SafetyExamState> {
  /// 获取安全教育考试题目用例
  final GetExamQuestionsUseCase getExamQuestionsUseCase;

  /// 构造函数
  SafetyExamBloc({
    required this.getExamQuestionsUseCase,
  }) : super(SafetyExamInitial()) {
    on<LoadExamQuestionsEvent>(_onLoadExamQuestions);
    on<SelectAnswerEvent>(_onSelectAnswer);
    on<ToggleMultipleAnswerEvent>(_onToggleMultipleAnswer);
    on<NextQuestionEvent>(_onNextQuestion);
    on<ViewResultsEvent>(_onViewResults);
    on<JumpToQuestionEvent>(_onJumpToQuestion);
  }

  /// 处理加载安全教育考试题目事件
  Future<void> _onLoadExamQuestions(
    LoadExamQuestionsEvent event,
    Emitter<SafetyExamState> emit,
  ) async {
    emit(SafetyExamLoading());
    try {
      // 从全局状态获取当前选中的实习计划ID
      String? planId;
      final planListBloc = GetIt.instance<PlanListGlobalBloc>();
      final planState = planListBloc.state;

      if (planState is PlanListGlobalLoadedState && planState.currentPlan != null) {
        planId = planState.currentPlan!.planId;
      }

      final questions = await getExamQuestionsUseCase(planId: planId);
      if (questions.isEmpty) {
        emit(const SafetyExamLoadFailure(message: '没有可用的试题'));
      } else {
        emit(SafetyExamInProgress(
          questions: questions,
          currentQuestionIndex: 0,
          userAnswers: const {},
          correctCount: 0,
          incorrectCount: 0,
        ));
      }
    } catch (e) {
      emit(SafetyExamLoadFailure(message: '加载试题失败: ${e.toString()}'));
    }
  }

  /// 处理选择答案事件（单选题）
  void _onSelectAnswer(
    SelectAnswerEvent event,
    Emitter<SafetyExamState> emit,
  ) {
    if (state is SafetyExamInProgress) {
      final currentState = state as SafetyExamInProgress;
      final currentQuestion = currentState.currentQuestion;

      // 如果是多选题，不处理此事件
      if (currentQuestion.isMultipleChoice) {
        return;
      }

      // 如果已经回答过这个问题，不做任何处理
      if (currentState.userAnswers.containsKey(currentQuestion.id)) {
        return;
      }

      // 更新用户答案映射
      final updatedUserAnswers = Map<String, List<String>>.from(currentState.userAnswers);
      updatedUserAnswers[currentQuestion.id] = [event.selectedOptionId];

      // 使用分数计算器重新计算分数
      final correctCount = ExamScoreCalculator.getCorrectCount(
        currentState.questions,
        updatedUserAnswers,
      );
      final incorrectCount = ExamScoreCalculator.getIncorrectCount(
        currentState.questions,
        updatedUserAnswers,
      );

      // 发出新状态
      emit(currentState.copyWith(
        userAnswers: updatedUserAnswers,
        selectedOptionIds: [event.selectedOptionId],
        correctCount: correctCount,
        incorrectCount: incorrectCount,
      ));
    }
  }

  /// 处理切换多选答案事件（多选题）
  void _onToggleMultipleAnswer(
    ToggleMultipleAnswerEvent event,
    Emitter<SafetyExamState> emit,
  ) {
    if (state is SafetyExamInProgress) {
      final currentState = state as SafetyExamInProgress;
      final currentQuestion = currentState.currentQuestion;

      // 如果不是多选题，不处理此事件
      if (!currentQuestion.isMultipleChoice) {
        return;
      }

      // 获取当前题目的用户答案
      final currentAnswers = List<String>.from(
        currentState.userAnswers[currentQuestion.id] ?? []
      );

      // 切换选项状态
      if (currentAnswers.contains(event.optionId)) {
        currentAnswers.remove(event.optionId);
      } else {
        currentAnswers.add(event.optionId);
      }

      // 更新用户答案映射
      final updatedUserAnswers = Map<String, List<String>>.from(currentState.userAnswers);
      if (currentAnswers.isEmpty) {
        updatedUserAnswers.remove(currentQuestion.id);
      } else {
        updatedUserAnswers[currentQuestion.id] = currentAnswers;
      }

      // 使用分数计算器重新计算分数
      final correctCount = ExamScoreCalculator.getCorrectCount(
        currentState.questions,
        updatedUserAnswers,
      );
      final incorrectCount = ExamScoreCalculator.getIncorrectCount(
        currentState.questions,
        updatedUserAnswers,
      );

      // 发出新状态
      emit(currentState.copyWith(
        userAnswers: updatedUserAnswers,
        selectedOptionIds: currentAnswers,
        correctCount: correctCount,
        incorrectCount: incorrectCount,
      ));
    }
  }

  /// 处理下一题事件
  void _onNextQuestion(
    NextQuestionEvent event,
    Emitter<SafetyExamState> emit,
  ) {
    if (state is SafetyExamInProgress) {
      final currentState = state as SafetyExamInProgress;
      
      // 如果当前题目未回答，不进行下一题操作
      if (!currentState.isCurrentQuestionAnswered) {
        return;
      }
      
      // 如果是最后一题，不做任何处理
      if (currentState.isLastQuestion) {
        return;
      }
      
      // 发出新状态，前进到下一题
      emit(currentState.copyWith(
        currentQuestionIndex: currentState.currentQuestionIndex + 1,
      ));
    }
  }

  /// 处理查看结果事件
  void _onViewResults(
    ViewResultsEvent event,
    Emitter<SafetyExamState> emit,
  ) {
    if (state is SafetyExamInProgress) {
      final currentState = state as SafetyExamInProgress;
      
      // 如果当前题目未回答，不进行查看结果操作
      if (!currentState.isCurrentQuestionAnswered) {
        return;
      }
      
      // 发出考试完成状态
      emit(SafetyExamCompleted(
        questions: currentState.questions,
        userAnswers: currentState.userAnswers,
        correctCount: currentState.correctCount,
        incorrectCount: currentState.incorrectCount,
      ));
    }
  }
  
  /// 处理跳转到指定题目事件
  void _onJumpToQuestion(
    JumpToQuestionEvent event,
    Emitter<SafetyExamState> emit,
  ) {
    if (state is SafetyExamInProgress) {
      final currentState = state as SafetyExamInProgress;
      
      // 验证题目索引是否有效
      if (event.questionIndex < 0 || event.questionIndex >= currentState.questions.length) {
        return;
      }
      
      // 发出新状态，跳转到指定题目
      emit(currentState.copyWith(
        currentQuestionIndex: event.questionIndex,
      ));
    }
  }
}
