/// -----
/// exam_option_card.dart
/// 
/// 考试选项卡片组件
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 选项状态枚举
enum OptionState {
  /// 未选中状态
  unselected,
  
  /// 选中且正确状态
  selectedCorrect,
  
  /// 选中且错误状态
  selectedIncorrect,
  
  /// 未选中但为正确答案状态（用于显示正确答案）
  unselectedCorrect,
}

/// 考试选项卡片组件
///
/// 显示一个考试选项，并根据选择状态显示不同的样式
class ExamOptionCard extends StatelessWidget {
  /// 选项数据
  final ExamOption option;

  /// 选项状态
  final OptionState state;

  /// 是否为多选题
  final bool isMultipleChoice;

  /// 是否已选中（用于多选题）
  final bool isSelected;

  /// 点击回调
  final VoidCallback? onTap;

  /// 构造函数
  const ExamOptionCard({
    Key? key,
    required this.option,
    required this.state,
    this.isMultipleChoice = false,
    this.isSelected = false,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 根据状态确定卡片样式
    Color backgroundColor;
    Color textColor;
    Widget? leadingIcon;
    Widget? trailingIcon;

    // 根据题型和状态确定前导图标
    if (isMultipleChoice) {
      // 多选题使用方形选择器
      leadingIcon = Container(
        width: 20.w,
        height: 20.w,
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : Colors.grey,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(4.r),
          color: isSelected ? AppTheme.primaryColor : Colors.transparent,
        ),
        child: isSelected
            ? Icon(
                Icons.check,
                size: 14.sp,
                color: Colors.white,
              )
            : null,
      );
    } else {
      // 单选题使用圆形选择器
      leadingIcon = Container(
        width: 20.w,
        height: 20.w,
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : Colors.grey,
            width: 2,
          ),
          shape: BoxShape.circle,
          color: isSelected ? AppTheme.primaryColor : Colors.transparent,
        ),
        child: isSelected
            ? Container(
                width: 8.w,
                height: 8.w,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
              )
            : null,
      );
    }

    switch (state) {
      case OptionState.unselected:
        backgroundColor = AppTheme.backgroundColor;
        textColor = Colors.black87;
        trailingIcon = null;
        break;
      case OptionState.selectedCorrect:
        backgroundColor = const Color(0xFFDDFFF1);
        textColor = const Color(0xFF04AE68);
        trailingIcon = const Icon(
          Icons.check_circle,
          color: Colors.green,
        );
        break;
      case OptionState.selectedIncorrect:
        backgroundColor = const Color(0xFFFFEBEB);
        textColor = const Color(0xFFFF4747);
        trailingIcon = const Icon(
          Icons.cancel,
          color: Colors.red,
        );
        break;
      case OptionState.unselectedCorrect:
        backgroundColor = Colors.green.shade100;
        textColor = Colors.green.shade900;
        trailingIcon = const Icon(
          Icons.check_circle,
          color: Colors.green,
        );
        break;
    }

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 6.0),
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(10.r),
          border: Border.all(
            color: state == OptionState.unselected
                ? Colors.transparent
                : state == OptionState.selectedCorrect || state == OptionState.unselectedCorrect
                    ? Colors.green
                    : Colors.red,
            width: 1,
          ),
        ),
        child: ListTile(
          contentPadding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 0.h),
          leading: leadingIcon,
          title: Text(
            '${option.id}: ${option.content}',
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          trailing: trailingIcon,
        ),
      ),
    );
  }
}
