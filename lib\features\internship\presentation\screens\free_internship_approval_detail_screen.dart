/// -----
/// free_internship_approval_detail_screen.dart
///
/// 免实习申请详情页面，用于查看和审批免实习申请
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/user_avatar.dart';
import 'package:flutter_demo/core/common/image_viewer_screen.dart';
import 'package:flutter_demo/features/internship/data/models/free_internship_application_model.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/student_header.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FreeInternshipApprovalDetailScreen extends StatefulWidget {
  final String approvalId;
  final String status;

  const FreeInternshipApprovalDetailScreen({
    Key? key,
    required this.approvalId,
    required this.status,
  }) : super(key: key);

  @override
  State<FreeInternshipApprovalDetailScreen> createState() =>
      _FreeInternshipApprovalDetailScreenState();
}

class _FreeInternshipApprovalDetailScreenState
    extends State<FreeInternshipApprovalDetailScreen> {
  late FreeInternshipApplicationModel _detailModel;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  // 加载数据
  void _loadData() {
    // 模拟网络请求延迟
    Future.delayed(const Duration(milliseconds: 500), () {
      // 获取示例数据
      final allApplications = FreeInternshipApplicationModel.getSampleData();

      // 根据ID查找对应的申请
      _detailModel = allApplications.firstWhere(
        (app) => app.id == widget.approvalId,
        orElse: () => allApplications.first,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: CustomAppBar(
        title: '免实习申请详情',
        centerTitle: true,
        showBackButton: true,
        actions: [
          IconButton(
            icon: Image.asset('assets/images/statistics_icon.png', width: 32.w, height: 32.h),
            onPressed: () {
              // TODO: 右侧图标点击事件
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 10),

                  // 学生信息头部
                  StudentHeader(
                    name: _detailModel.studentName,
                    avatar: _detailModel.studentAvatar,
                  ),

                  const SizedBox(height: 10),

                  // 免实习申请信息
                  _buildApplicationInfo(),

                  // 审批状态
                  _buildApprovalStatus(),

                  // 底部空间，避免按钮遮挡
                  const SizedBox(height: 80),
                ],
              ),
            ),
      // 底部按钮
      bottomNavigationBar: _isLoading ? null : _buildBottomBar(),
    );
  }



  // 免实习申请信息
  Widget _buildApplicationInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            children: [
              Image.asset('assets/images/job_information_icon.png', width: 25.w, height: 28.h),
              const SizedBox(width: 8),
              Text(
                '免实习申请信息',
                style: TextStyle(
                  fontSize: 30.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.black333,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
          const SizedBox(height: 16),

          // 学生去向
          _buildInfoRow('学生去向', _detailModel.studentDestination),

          const SizedBox(height: 16),

          // 证明文件
          _buildAttachmentRow('证明文件', _detailModel.attachments),
        ],
      ),
    );
  }

  // 审批状态
  Widget _buildApprovalStatus() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            children: [
              Image.asset('assets/images/internship_information_icon.png', width: 25.w, height: 28.h),
              const SizedBox(width: 8),
              Text(
                '审批状态',
                style: TextStyle(
                  fontSize: 30.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.black333,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
          const SizedBox(height: 16),

          // 审批人信息
          Row(
            children: [
              // 头像
              UserAvatar.small(
                imageUrl: 'assets/images/teacher_avatar.png',
                type: AvatarType.asset,
                userName: '冯项老师',
              ),
              const SizedBox(width: 12),
              // 审批人信息
              const Text(
                '冯项老师 (班主任)',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              // 审批状态
              _buildApprovalStatusTag(_detailModel.status),
            ],
          ),

          // 如果是已通过状态，显示驳回按钮
          if (_detailModel.status == '已通过') ...[
            const SizedBox(height: 16),
            Align(
              alignment: Alignment.centerRight,
              child: Container(
                width: 120,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red),
                ),
                child: TextButton(
                  onPressed: () => _showRejectDialog(),
                  style: TextButton.styleFrom(
                    backgroundColor: Colors.white,
                    foregroundColor: Colors.red,
                    padding: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    '驳回',
                    style: TextStyle(fontSize: 16,fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // 底部导航栏
  Widget? _buildBottomBar() {
    // 根据状态显示不同的底部按钮
    if (_detailModel.status == '待审批') {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Row(
          children: [
            // 驳回按钮
            Expanded(
              child: ElevatedButton(
                onPressed: () => _showRejectDialog(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.red,
                  side: const BorderSide(color: Colors.red),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  '驳回',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
            const SizedBox(width: 16),
            // 通过按钮
            Expanded(
              child: ElevatedButton(
                onPressed: () => _showApproveDialog(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text(
                  '通过',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      // 已通过或已驳回状态，不显示底部按钮
      return null;
    }
  }

  // 信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  // 附件行
  Widget _buildAttachmentRow(String label, List<AttachmentInfo> attachments) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: attachments.isNotEmpty
              ? Stack(
                  children: [
                    // 附件图片
                    InkWell(
                      onTap: () => _viewAttachment(attachments.first),
                      child: Container(
                        width: 98,
                        height: 98,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey[300]!, width: 1),
                        ),
                        clipBehavior: Clip.antiAlias,
                        child: Image.asset(
                          'assets/images/certificate_sample.png',
                          width: 98,
                          height: 98,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Image.network(
                            'https://pic1.zhimg.com/v2-a58fa2ab84be291418da2652805f8270_b.jpg',
                            width: 98,
                            height: 98,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Container(
                              width: 98,
                              height: 98,
                              color: Colors.grey[200],
                              child: Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.insert_drive_file,
                                      size: 36,
                                      color: Colors.grey,
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      attachments.first.name,
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: AppTheme.primaryColor,
                                      ),
                                      textAlign: TextAlign.center,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),

                    // 状态图标
                    if (_detailModel.status != '待审批')
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Image.asset(
                          _detailModel.status == '已通过'
                              ? 'assets/images/passed_icon.png'
                              : 'assets/images/rejected_icon.png',
                          width: 60,
                          height: 60,
                        ),
                      ),
                  ],
                )
              : const Text(
                  '无',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
        ),
      ],
    );
  }

  // 审批状态标签
  Widget _buildApprovalStatusTag(String status) {
    Color bgColor;
    Color textColor;
    IconData? icon;

    switch (status) {
      case '待审批':
        bgColor = Colors.blue.withOpacity(0.1);
        textColor = AppTheme.primaryColor;
        icon = null;
        break;
      case '已通过':
        bgColor = Colors.green.withOpacity(0.1);
        textColor = Colors.green;
        icon = Icons.check_circle;
        break;
      case '已驳回':
        bgColor = Colors.red.withOpacity(0.1);
        textColor = Colors.red;
        icon = Icons.cancel;
        break;
      default:
        bgColor = Colors.grey.withOpacity(0.1);
        textColor = Colors.grey;
        icon = null;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 16, color: textColor),
            const SizedBox(width: 4),
          ],
          Text(
            status,
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // 查看附件
  void _viewAttachment(AttachmentInfo attachment) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ImageViewerScreen(
          imageUrl: attachment.url.isNotEmpty
              ? attachment.url
              : 'https://pic1.zhimg.com/v2-a58fa2ab84be291418da2652805f8270_b.jpg',
          title: attachment.name,
          isNetworkImage: true,
        ),
      ),
    );
  }

  // 显示通过确认对话框
  void _showApproveDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认通过'),
        content: const Text('确定要通过该免实习申请吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _approveApplication();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  // 显示驳回确认对话框
  void _showRejectDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认驳回'),
        content: const Text('确定要驳回该免实习申请吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _rejectApplication();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  // 通过申请
  void _approveApplication() {
    // 模拟网络请求
    setState(() {
      _isLoading = true;
    });

    // 模拟延迟
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
          // 更新状态
          _detailModel = FreeInternshipApplicationModel(
            id: _detailModel.id,
            studentName: _detailModel.studentName,
            studentAvatar: _detailModel.studentAvatar,
            studentDestination: _detailModel.studentDestination,
            applyDate: _detailModel.applyDate,
            status: '已通过',
            attachments: _detailModel.attachments,
          );
        });

        // 显示成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('申请已通过')),
        );

        // 返回上一页并传递更新标志
        Navigator.pop(context, true);
      }
    });
  }

  // 驳回申请
  void _rejectApplication() {
    // 模拟网络请求
    setState(() {
      _isLoading = true;
    });

    // 模拟延迟
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
          // 更新状态
          _detailModel = FreeInternshipApplicationModel(
            id: _detailModel.id,
            studentName: _detailModel.studentName,
            studentAvatar: _detailModel.studentAvatar,
            studentDestination: _detailModel.studentDestination,
            applyDate: _detailModel.applyDate,
            status: '已驳回',
            attachments: _detailModel.attachments,
          );
        });

        // 显示成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('申请已驳回')),
        );

        // 返回上一页并传递更新标志
        Navigator.pop(context, true);
      }
    });
  }
}
