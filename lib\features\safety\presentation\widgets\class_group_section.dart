import 'package:flutter/material.dart';
import 'student_safety_list_item.dart';

/// 班级分组组件
///
/// 用于展示班级内学生的安全教育试题情况
/// 支持展开/收起功能，可以查看或隐藏班级内的学生列表
///
/// <AUTHOR>
/// @date 2025-04-28
/// @version 1.0
class ClassGroupSection extends StatefulWidget {
  /// 班级名称
  final String className;

  /// 学生列表数据
  final List<Map<String, dynamic>> students;

  const ClassGroupSection({Key? key, required this.className, required this.students}) : super(key: key);

  @override
  State<ClassGroupSection> createState() => _ClassGroupSectionState();
}

class _ClassGroupSectionState extends State<ClassGroupSection> {
  /// 是否展开学生列表
  bool _expanded = true;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 班级标题栏
        InkWell(
          onTap: () => setState(() => _expanded = !_expanded),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            decoration: const BoxDecoration(
              color: Color(0xFFF8F9FC),
            ),
            child: Row(
              children: [
                // 左侧蓝色竖线
                Container(
                  width: 3,
                  height: 16,
                  color: const Color(0xFF4B7CFF),
                ),
                const SizedBox(width: 12),

                // 班级名称
                Expanded(
                  child: Text(
                    widget.className,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 15,
                      color: Color(0xFF333333),
                    ),
                  ),
                ),

                // 收起文字
                const Text(
                  '收起',
                  style: TextStyle(
                    fontSize: 13,
                    color: Color(0xFF999999),
                  ),
                ),

                // 向上箭头
                const Icon(
                  Icons.keyboard_arrow_up,
                  color: Color(0xFF999999),
                  size: 20,
                ),
              ],
            ),
          ),
        ),

        // 学生列表
        if (_expanded)
          Column(
            children: _buildStudentList(),
          ),
      ],
    );
  }

  /// 构建学生列表，每个学生之间有间距
  List<Widget> _buildStudentList() {
    final List<Widget> studentWidgets = [];

    for (int i = 0; i < widget.students.length; i++) {
      // 添加学生项
      studentWidgets.add(
        Container(
          margin: EdgeInsets.only(bottom: i < widget.students.length - 1 ? 20 : 0),
          child: StudentSafetyListItem(student: widget.students[i])
        )
      );
    }

    return studentWidgets;
  }
}
