/// -----
/// teacher_safety_education_repository_impl.dart
/// 
/// 教师端安全教育考试仓库实现
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/models/teacher_safety_education_response.dart';
import '../../domain/repositories/teacher_safety_education_repository.dart';
import '../datasources/remote/teacher_safety_education_remote_data_source.dart';

/// 教师端安全教育考试仓库实现
/// 
/// 实现获取教师端安全教育考试数据的具体逻辑，包括网络检查和错误处理
class TeacherSafetyEducationRepositoryImpl implements TeacherSafetyEducationRepository {
  final TeacherSafetyEducationRemoteDataSource _remoteDataSource;
  final NetworkInfo _networkInfo;
  
  static const String _tag = 'TeacherSafetyEducationRepository';

  TeacherSafetyEducationRepositoryImpl({
    required TeacherSafetyEducationRemoteDataSource remoteDataSource,
    required NetworkInfo networkInfo,
  })  : _remoteDataSource = remoteDataSource,
        _networkInfo = networkInfo;

  @override
  Future<Either<Failure, TeacherSafetyEducationResponse>> getTeacherSafetyEducationData(String planId) async {
    try {
      Logger.info(_tag, '开始获取教师端安全教育考试数据，planId: $planId');

      // 检查网络连接
      if (await _networkInfo.isConnected) {
        final responseModel = await _remoteDataSource.getTeacherSafetyEducationData(planId);
        final response = responseModel.toDomain();
        
        Logger.info(_tag, '成功获取教师端安全教育考试数据');
        return Right(response);
      } else {
        Logger.warning(_tag, '网络连接不可用');
        return const Left(NetworkFailure('网络连接不可用，请检查网络设置'));
      }
    } on ServerException catch (e) {
      Logger.error(_tag, '服务器异常: ${e.message}');
      return Left(ServerFailure(e.message));
    } catch (e) {
      Logger.error(_tag, '未知异常: $e');
      return Left(ServerFailure('获取教师端安全教育考试数据失败: $e'));
    }
  }
}
