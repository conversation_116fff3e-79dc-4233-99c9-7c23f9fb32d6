/// -----
/// uploaded_files_widget.dart
///
/// 已上传文件展示组件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/upload/models/file_upload_item.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 已上传文件展示组件
class UploadedFilesWidget extends StatelessWidget {
  /// 已上传的文件列表
  final List<UploadedFile> files;
  /// 当前状态
  final FileUploadStatus status;

  const UploadedFilesWidget({
    Key? key,
    required this.files,
    required this.status,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 已上传文件标题
          Text(
            '已上传文件',
            style: TextStyle(
              fontSize: 30.sp,
              color: AppTheme.black333,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 21.h),

          // 文件预览网格
          if (files.isNotEmpty)
            Container(

              padding: EdgeInsets.all(30.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 12.w,
                  mainAxisSpacing: 12.h,
                  childAspectRatio: 0.75,
                ),
                itemCount: files.length,
                itemBuilder: (context, index) {
                  final file = files[index];
                  final isLastFile = index == files.length - 1;

                  return Stack(
                    children: [
                      // 文件预览
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10.r),
                        ),
                        child: file.previewImage != null
                            ? ClipRRect(
                          borderRadius: BorderRadius.circular(8.r),
                          child: Container(
                            color: Colors.grey[100],
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.description,
                                    size: 32.w,
                                    color: Colors.grey[400],
                                  )
                                ],
                              ),
                            ),
                          ),
                        )
                            : _buildDefaultFileIcon(),
                      ),

                      // 状态印章（只在最后一个文件上显示）
                      if (isLastFile && status != FileUploadStatus.uploaded)
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Center(
                              child: _buildStatusStamp(),
                            ),
                          ),
                        ),
                    ],
                  );
                },
              ),
            )

        ],
      ),
    );
  }

  /// 构建默认文件图标
  Widget _buildDefaultFileIcon() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Center(
        child: Icon(
          Icons.description,
          size: 40.w,
          color: const Color(0xFF999999),
        ),
      ),
    );
  }

  /// 构建状态印章
  Widget _buildStatusStamp() {
    Widget stamp = Image.asset('assets/images/passed_icon.png');
    switch (status) {
      case FileUploadStatus.approved:
        stamp = Image.asset('assets/images/passed_icon.png');
        break;
      case FileUploadStatus.rejected:
        stamp = Image.asset('assets/images/rejected_icon.png');
        break;
      default:
        return stamp;
    }
    return stamp;
  }
}
