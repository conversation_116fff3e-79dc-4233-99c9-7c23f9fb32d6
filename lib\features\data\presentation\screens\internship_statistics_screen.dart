/// -----------------------------------------------------------------------------
/// internship_statistics_screen.dart
///
/// 实习申请统计页面，用于展示实习申请的统计数据和学生列表
///
/// 功能：
/// 1. 展示实习申请统计数据（已提交、实习中、已驳回）
/// 2. 提供未提交/已提交的分类查看
/// 3. 展示学生列表及其认证状态
///
/// 使用方法：
/// ```dart
/// Navigator.push(
///   context,
///   MaterialPageRoute(builder: (context) => const InternshipStatisticsScreen()),
/// );
/// ```
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/sticky_tab_bar.dart';
import 'package:flutter_demo/core/widgets/status_tag.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';

/// 学生数据模型
class StudentModel {
  final String id;
  final String name;
  final String phone;
  final String avatar;
  final bool isVerified;

  StudentModel({
    required this.id,
    required this.name,
    required this.phone,
    required this.avatar,
    required this.isVerified,
  });

  /// 获取示例数据
  static List<StudentModel> getSampleData() {
    return [
      StudentModel(
        id: '1',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        isVerified: false,
      ),
      StudentModel(
        id: '2',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        isVerified: true,
      ),
      StudentModel(
        id: '3',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        isVerified: true,
      ),
      StudentModel(
        id: '4',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        isVerified: true,
      ),
      StudentModel(
        id: '5',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        isVerified: true,
      ),
      StudentModel(
        id: '6',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        isVerified: true,
      ),
      StudentModel(
        id: '7',
        name: '李成儒',
        phone: '13569874562',
        avatar: 'https://via.placeholder.com/80x80/4B7CFF/FFFFFF?text=李',
        isVerified: true,
      ),
    ];
  }
}

/// 实习申请统计页面
class InternshipStatisticsScreen extends StatefulWidget {
  const InternshipStatisticsScreen({Key? key}) : super(key: key);

  @override
  State<InternshipStatisticsScreen> createState() => _InternshipStatisticsScreenState();
}

class _InternshipStatisticsScreenState extends State<InternshipStatisticsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final String _courseName = '2021级市场销售2023-2024实习学年第二学期岗位实习';
  late List<StudentModel> _allStudents;
  late List<StudentModel> _unsubmittedStudents;
  late List<StudentModel> _submittedStudents;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 加载数据
  void _loadData() {
    _allStudents = StudentModel.getSampleData();
    // 模拟数据分类
    _unsubmittedStudents = _allStudents.where((s) => !s.isVerified).toList();
    _submittedStudents = _allStudents.where((s) => s.isVerified).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: const CustomAppBar(
        title: '实习申请统计',
        centerTitle: true,
      ),
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            // 课程头部和统计数据
            SliverToBoxAdapter(
              child: Column(
                children: [
                  // 课程头部 - 自动从全局状态获取实习计划数据
                  const CourseHeaderSection(),
                  // 第一条分割线：课程头部和统计数据之间
                  Container(
                    height: 1.h,
                    color: AppTheme.dividerColor,
                    margin: EdgeInsets.symmetric(horizontal: 40.w),
                  ),
                  // 统计数据区域
                  _buildStatisticsSection(),
                  // 第二条分割线：统计数据和Tab栏之间
                  Container(
                    height: 1.h,
                    color: AppTheme.dividerColor,
                    margin: EdgeInsets.symmetric(horizontal: 40.w),
                  ),
                ],
              ),
            ),
            // 固定的Tab栏
            SliverPersistentHeader(
              pinned: true,
              delegate: StickyTabBarDelegate(
                tabBar: TabBar(
                  controller: _tabController,
                  labelColor: AppTheme.blue2165f6,
                  unselectedLabelColor: AppTheme.black666,
                  indicatorColor: AppTheme.blue2165f6,
                  indicatorWeight: 3.0,
                  labelStyle: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.w600,
                  ),
                  unselectedLabelStyle: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.normal,
                  ),
                  tabs: [
                    CountedTab(
                      text: '未提交',
                      count: _unsubmittedStudents.length,
                      showCountAsBadge: true,
                    ),
                    const Tab(text: '已提交'),
                  ],
                ),
              ),
            ),
          ];
        },
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildStudentList(_unsubmittedStudents),
            _buildStudentList(_submittedStudents),
          ],
        ),
      ),
    );
  }

  /// 构建统计数据区域
  Widget _buildStatisticsSection() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(40.w),
      child: Column(
        children: [
          // 标题行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '上岗统计',
                style: TextStyle(
                  fontSize: 28.sp,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.black333,
                ),
              ),
              Text(
                '2023.05.15',
                style: TextStyle(
                  fontSize: 24.sp,
                  color: AppTheme.black999,
                ),
              ),
            ],
          ),
          SizedBox(height: 32.h),
          // 统计数字行
          Row(
            children: [
              Expanded(
                child: _buildStatisticItem('24', '岗位已提交'),
              ),
              Container(
                width: 1.w,
                height: 60.h,
                color: AppTheme.dividerColor,
              ),
              Expanded(
                child: _buildStatisticItem('38', '实习中'),
              ),
              Container(
                width: 1.w,
                height: 60.h,
                color: AppTheme.dividerColor,
              ),
              Expanded(
                child: _buildStatisticItem('6', '已较回'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatisticItem(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: TextStyle(
            fontSize: 48.sp,
            fontWeight: FontWeight.bold,
            color: AppTheme.blue2165f6,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          label,
          style: TextStyle(
            fontSize: 24.sp,
            color: AppTheme.black666,
          ),
        ),
      ],
    );
  }

  /// 构建学生列表
  Widget _buildStudentList(List<StudentModel> students) {
    if (students.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 120.w,
              color: AppTheme.black999,
            ),
            SizedBox(height: 32.h),
            Text(
              '暂无数据',
              style: TextStyle(
                fontSize: 28.sp,
                color: AppTheme.black999,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 20.h),
      itemCount: students.length,
      itemBuilder: (context, index) {
        return _buildStudentItem(students[index]);
      },
    );
  }

  /// 构建学生项
  Widget _buildStudentItem(StudentModel student) {
    return Container(
      margin: EdgeInsets.only(bottom: 20.h),
      padding: EdgeInsets.all(32.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 头像
          CircleAvatar(
            radius: 40.r,
            backgroundImage: NetworkImage(student.avatar),
            backgroundColor: AppTheme.blue2165f6.withOpacity(0.1),
          ),
          SizedBox(width: 24.w),
          // 姓名
          Text(
            student.name,
            style: TextStyle(
              fontSize: 28.sp,
              fontWeight: FontWeight.w600,
              color: AppTheme.black333,
            ),
          ),
          SizedBox(width: 32.w),
          // 电话
          GestureDetector(
            onTap: () => _makePhoneCall(student.phone),
            child: Row(
              children: [
                Icon(
                  Icons.phone,
                  size: 32.w,
                  color: AppTheme.black666,
                ),
                SizedBox(width: 12.w),
                Text(
                  student.phone,
                  style: TextStyle(
                    fontSize: 24.sp,
                    color: AppTheme.black666,
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          // 状态标签
          StatusTag(
            text: student.isVerified ? '已认证' : '未认证',
            type: student.isVerified ? StatusTagType.verified : StatusTagType.unverified,
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 3.h),
          ),
        ],
      ),
    );
  }

  /// 拨打电话
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    try {
      if (await canLaunchUrl(launchUri)) {
        await launchUrl(launchUri);
      }
    } catch (e) {
      // 处理错误
      debugPrint('无法拨打电话: $phoneNumber');
    }
  }
}

