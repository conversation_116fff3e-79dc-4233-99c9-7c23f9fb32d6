/// -----
/// user_avatar.dart
///
/// 通用头像组件，支持网络图片、本地图片、资源图片等多种头像类型
/// 提供统一的样式、大小和错误处理机制
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// 头像类型枚举
enum AvatarType {
  /// 网络图片
  network,
  /// 本地文件
  file,
  /// 资源图片
  asset,
}

/// 通用头像组件
/// 
/// 支持多种头像类型和自定义样式
/// 提供统一的错误处理和默认头像显示
class UserAvatar extends StatelessWidget {
  /// 头像图片路径或URL
  final String? imageUrl;
  
  /// 头像类型
  final AvatarType type;
  
  /// 头像大小（半径）
  final double radius;
  
  /// 背景颜色
  final Color? backgroundColor;
  
  /// 边框颜色
  final Color? borderColor;
  
  /// 边框宽度
  final double borderWidth;
  
  /// 默认头像图标
  final IconData? defaultIcon;
  
  /// 默认头像图标大小
  final double? defaultIconSize;
  
  /// 默认头像图标颜色
  final Color? defaultIconColor;
  
  /// 默认头像资源路径
  final String? defaultAssetPath;
  
  /// 显示用户名首字母（当没有头像时）
  final String? userName;
  
  /// 用户名首字母文字样式
  final TextStyle? userNameStyle;
  
  /// 点击回调
  final VoidCallback? onTap;

  const UserAvatar({
    Key? key,
    this.imageUrl,
    this.type = AvatarType.network,
    this.radius = 22.0,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth = 0.0,
    this.defaultIcon,
    this.defaultIconSize,
    this.defaultIconColor,
    this.defaultAssetPath,
    this.userName,
    this.userNameStyle,
    this.onTap,
  }) : super(key: key);

  /// 创建网络头像
  factory UserAvatar.network({
    required String imageUrl,
    double radius = 22.0,
    Color? backgroundColor,
    Color? borderColor,
    double borderWidth = 0.0,
    String? userName,
    TextStyle? userNameStyle,
    VoidCallback? onTap,
  }) {
    return UserAvatar(
      imageUrl: imageUrl,
      type: AvatarType.network,
      radius: radius,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      borderWidth: borderWidth,
      userName: userName,
      userNameStyle: userNameStyle,
      onTap: onTap,
    );
  }

  /// 创建本地文件头像
  factory UserAvatar.file({
    required String filePath,
    double radius = 22.0,
    Color? backgroundColor,
    Color? borderColor,
    double borderWidth = 0.0,
    String? userName,
    TextStyle? userNameStyle,
    VoidCallback? onTap,
  }) {
    return UserAvatar(
      imageUrl: filePath,
      type: AvatarType.file,
      radius: radius,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      borderWidth: borderWidth,
      userName: userName,
      userNameStyle: userNameStyle,
      onTap: onTap,
    );
  }

  /// 创建资源头像
  factory UserAvatar.asset({
    required String assetPath,
    double radius = 22.0,
    Color? backgroundColor,
    Color? borderColor,
    double borderWidth = 0.0,
    String? userName,
    TextStyle? userNameStyle,
    VoidCallback? onTap,
  }) {
    return UserAvatar(
      imageUrl: assetPath,
      type: AvatarType.asset,
      radius: radius,
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      borderWidth: borderWidth,
      userName: userName,
      userNameStyle: userNameStyle,
      onTap: onTap,
    );
  }

  /// 创建小尺寸头像（28px）
  factory UserAvatar.small({
    String? imageUrl,
    AvatarType type = AvatarType.network,
    Color? backgroundColor,
    String? userName,
    VoidCallback? onTap,
  }) {
    return UserAvatar(
      imageUrl: imageUrl,
      type: type,
      radius: 14.r, // 28px / 2
      backgroundColor: backgroundColor,
      userName: userName,
      onTap: onTap,
    );
  }

  /// 创建中等尺寸头像（56px）
  factory UserAvatar.medium({
    String? imageUrl,
    AvatarType type = AvatarType.network,
    Color? backgroundColor,
    Color? borderColor,
    double borderWidth = 0.0,
    String? userName,
    VoidCallback? onTap,
  }) {
    return UserAvatar(
      imageUrl: imageUrl,
      type: type,
      radius: 28.r, // 56px / 2
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      borderWidth: borderWidth,
      userName: userName,
      onTap: onTap,
    );
  }

  /// 创建大尺寸头像（88px）
  factory UserAvatar.large({
    String? imageUrl,
    AvatarType type = AvatarType.network,
    Color? backgroundColor,
    Color? borderColor,
    double borderWidth = 0.0,
    String? userName,
    VoidCallback? onTap,
  }) {
    return UserAvatar(
      imageUrl: imageUrl,
      type: type,
      radius: 44.r, // 88px / 2
      backgroundColor: backgroundColor,
      borderColor: borderColor,
      borderWidth: borderWidth,
      userName: userName,
      onTap: onTap,
    );
  }

  /// 创建超大尺寸头像（140px）
  factory UserAvatar.extraLarge({
    String? imageUrl,
    AvatarType type = AvatarType.network,
    Color? backgroundColor,
    Color? borderColor,
    double borderWidth = 6.0,
    String? userName,
    VoidCallback? onTap,
  }) {
    return UserAvatar(
      imageUrl: imageUrl,
      type: type,
      radius: 70.r, // 140px / 2
      backgroundColor: backgroundColor,
      borderColor: borderColor ?? Colors.white,
      borderWidth: borderWidth,
      userName: userName,
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget avatar = _buildAvatar();

    // 如果有边框，添加边框装饰
    if (borderWidth > 0 && borderColor != null) {
      avatar = Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: borderColor!,
            width: borderWidth,
          ),
        ),
        child: avatar,
      );
    }

    // 如果有点击事件，包装在GestureDetector中
    if (onTap != null) {
      avatar = GestureDetector(
        onTap: onTap,
        child: avatar,
      );
    }

    return avatar;
  }

  /// 构建头像组件
  Widget _buildAvatar() {
    return CircleAvatar(
      radius: radius,
      backgroundColor: backgroundColor ?? Colors.grey[300],
      child: ClipOval(
        child: _buildAvatarContent(),
      ),
    );
  }

  /// 构建头像内容
  Widget _buildAvatarContent() {
    // 如果有图片路径，尝试加载图片
    if (imageUrl != null && imageUrl!.isNotEmpty) {
      return _buildImageWidget();
    }

    // 如果有用户名，显示首字母
    if (userName != null && userName!.isNotEmpty) {
      return _buildUserNameWidget();
    }

    // 显示默认图标或默认头像
    return _buildDefaultWidget();
  }

  /// 构建图片组件
  Widget _buildImageWidget() {
    Widget imageWidget;

    switch (type) {
      case AvatarType.network:
        imageWidget = Image.network(
          imageUrl!,
          width: radius * 2,
          height: radius * 2,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
        );
        break;
      case AvatarType.file:
        imageWidget = Image.file(
          File(imageUrl!),
          width: radius * 2,
          height: radius * 2,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
        );
        break;
      case AvatarType.asset:
        imageWidget = Image.asset(
          imageUrl!,
          width: radius * 2,
          height: radius * 2,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
        );
        break;
    }

    return imageWidget;
  }

  /// 构建用户名首字母组件
  Widget _buildUserNameWidget() {
    final firstLetter = userName!.substring(0, 1).toUpperCase();
    return Center(
      child: Text(
        firstLetter,
        style: userNameStyle ?? TextStyle(
          fontSize: radius * 0.6,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
    );
  }

  /// 构建默认组件
  Widget _buildDefaultWidget() {
    // 如果指定了默认资源路径
    if (defaultAssetPath != null) {
      return Image.asset(
        defaultAssetPath!,
        width: radius * 2,
        height: radius * 2,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildIconWidget(),
      );
    }

    return _buildIconWidget();
  }

  /// 构建图标组件
  Widget _buildIconWidget() {
    return Icon(
      defaultIcon ?? Icons.person,
      size: defaultIconSize ?? radius * 0.8,
      color: defaultIconColor ?? Colors.grey[600],
    );
  }

  /// 构建错误时的组件
  Widget _buildErrorWidget() {
    // 尝试加载SVG默认头像
    try {
      return SvgPicture.asset(
        'assets/images/default_avatar.svg',
        width: radius * 2,
        height: radius * 2,
        fit: BoxFit.cover,
      );
    } catch (e) {
      // 如果SVG也加载失败，显示默认图标
      return _buildIconWidget();
    }
  }
}
