/// -----
/// file_approval_list_screen.dart
///
/// 文件审批列表页面，显示特定文件类型的审批列表
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/widgets/approval_tab_bar.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/student_file_list_item.dart';
import 'package:flutter_demo/features/approval/presentation/pages/file_approval_preview_screen.dart';

import 'package:flutter_demo/features/approval/core/utils/file_type_mapper.dart';
import 'package:flutter_demo/features/approval/domain/entities/student_file_approval.dart';
import 'package:flutter_demo/features/approval/presentation/bloc/student_file_approval_list/student_file_approval_list_bloc.dart';
import 'package:flutter_demo/features/approval/presentation/bloc/student_file_approval_list/student_file_approval_list_event.dart';
import 'package:flutter_demo/features/approval/presentation/bloc/student_file_approval_list/student_file_approval_list_state.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FileApprovalListScreen extends StatefulWidget {
  /// 文件类型ID
  final String fileId;

  const FileApprovalListScreen({
    Key? key,
    required this.fileId,
  }) : super(key: key);

  @override
  State<FileApprovalListScreen> createState() => _FileApprovalListScreenState();
}

class _FileApprovalListScreenState extends State<FileApprovalListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late StudentFileApprovalListBloc _pendingBloc;
  late StudentFileApprovalListBloc _approvedBloc;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _pendingBloc = GetIt.instance<StudentFileApprovalListBloc>();
    _approvedBloc = GetIt.instance<StudentFileApprovalListBloc>();
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pendingBloc.close();
    _approvedBloc.close();
    super.dispose();
  }

  void _loadData() {
    final planListBloc = GetIt.instance<PlanListGlobalBloc>();
    final state = planListBloc.state;

    if (state is PlanListGlobalLoadedState && state.currentPlan != null) {
      final planId = state.currentPlan!.planId;
      // 加载待审批列表 (type = 0)
      _pendingBloc.add(LoadStudentFileApprovalListEvent(planId: planId, type: 0));
      // 加载已审批列表 (type = 1)
      _approvedBloc.add(LoadStudentFileApprovalListEvent(planId: planId, type: 1));
    }
  }



  @override
  Widget build(BuildContext context) {
    return BlocListener<PlanListGlobalBloc, PlanListGlobalState>(
      bloc: GetIt.instance<PlanListGlobalBloc>(),
      listener: (context, state) {
        // 当实习计划切换时，重新加载数据
        if (state is PlanListGlobalLoadedState) {
          _loadData();
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF9F9F9),
        appBar: const CustomAppBar(
          title: '文件列表',
          centerTitle: true,
          showBackButton: true,
        ),
        body: Column(
          children: [
            // 课程头部 - 自动从全局状态获取实习计划数据
            const CourseHeaderSection(),

            // 标签栏
            BlocBuilder<StudentFileApprovalListBloc, StudentFileApprovalListState>(
              bloc: _pendingBloc,
              builder: (context, state) {
                int pendingCount = 0;
                if (state is StudentFileApprovalListLoaded) {
                  pendingCount = state.studentFileApprovalList.length;
                }
                return ApprovalTabBar(
                  controller: _tabController,
                  pendingCount: pendingCount,
                );
              },
            ),

            // 页面内容
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildPendingList(),
                  _buildApprovedList(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 待审批列表
  Widget _buildPendingList() {
    return BlocBuilder<StudentFileApprovalListBloc, StudentFileApprovalListState>(
      bloc: _pendingBloc,
      builder: (context, state) {
        if (state is StudentFileApprovalListLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is StudentFileApprovalListError) {
          return _buildErrorWidget(state.message, () => _loadData());
        } else if (state is StudentFileApprovalListLoaded) {
          if (state.studentFileApprovalList.isEmpty) {
            return _buildEmptyWidget('暂无待审批文件');
          }
          return RefreshIndicator(
            onRefresh: () async => _refreshPendingList(),
            child: ListView.builder(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              itemCount: state.studentFileApprovalList.length,
              itemBuilder: (context, index) {
                final studentApproval = state.studentFileApprovalList[index];
                return _buildStudentFileListItem(studentApproval, true);
              },
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  // 已审批列表
  Widget _buildApprovedList() {
    return BlocBuilder<StudentFileApprovalListBloc, StudentFileApprovalListState>(
      bloc: _approvedBloc,
      builder: (context, state) {
        if (state is StudentFileApprovalListLoading) {
          return const Center(child: CircularProgressIndicator());
        } else if (state is StudentFileApprovalListError) {
          return _buildErrorWidget(state.message, () => _loadData());
        } else if (state is StudentFileApprovalListLoaded) {
          if (state.studentFileApprovalList.isEmpty) {
            return _buildEmptyWidget('暂无已审批文件');
          }
          return RefreshIndicator(
            onRefresh: () async => _refreshApprovedList(),
            child: ListView.builder(
              padding: EdgeInsets.symmetric(vertical: 16.h),
              itemCount: state.studentFileApprovalList.length,
              itemBuilder: (context, index) {
                final studentApproval = state.studentFileApprovalList[index];
                return _buildStudentFileListItem(studentApproval, false);
              },
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  /// 刷新待审批列表
  void _refreshPendingList() {
    final planListBloc = GetIt.instance<PlanListGlobalBloc>();
    final state = planListBloc.state;

    if (state is PlanListGlobalLoadedState && state.currentPlan != null) {
      _pendingBloc.add(RefreshStudentFileApprovalListEvent(
        planId: state.currentPlan!.planId,
        type: 0,
      ));
    }
  }

  /// 刷新已审批列表
  void _refreshApprovedList() {
    final planListBloc = GetIt.instance<PlanListGlobalBloc>();
    final state = planListBloc.state;

    if (state is PlanListGlobalLoadedState && state.currentPlan != null) {
      _approvedBloc.add(RefreshStudentFileApprovalListEvent(
        planId: state.currentPlan!.planId,
        type: 1,
      ));
    }
  }

  /// 构建错误提示组件
  Widget _buildErrorWidget(String message, VoidCallback onRetry) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '加载失败',
            style: TextStyle(
              fontSize: 16.sp,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            message,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: onRetry,
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 构建空数据提示组件
  Widget _buildEmptyWidget(String message) {
    return Center(
      child: Text(
        message,
        style: TextStyle(
          fontSize: 16.sp,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  /// 构建学生文件列表项
  Widget _buildStudentFileListItem(StudentFileApproval studentApproval, bool isPending) {
    // 转换为StudentFileInfo格式
    final studentFileInfo = StudentFileInfo(
      studentId: studentApproval.studentId,
      studentName: studentApproval.studentName,
      studentAvatar: AppConstants.avatar1, // 使用默认头像
      status: isPending ? '待审批' : '已审批',
      files: studentApproval.fileList.map((fileDetail) {
        final fileTypeInfo = FileTypeMapper.getFileTypeInfo(fileDetail.fileType);
        return FileInfo(
          fileId: fileDetail.id,
          fileName: fileDetail.fileType, // 直接使用API返回的文件类型名称
          iconPath: fileTypeInfo.iconPath,
          onTap: () => _viewFile(fileDetail, studentApproval.studentName, isPending),
        );
      }).toList(),
    );

    return StudentFileListItem(
      studentFileInfo: studentFileInfo,
      isPending: isPending,
    );
  }

  /// 查看文件
  void _viewFile(FileApprovalDetail fileDetail, String studentName, bool isPending) {
    // 跳转到文件审批预览页面，传递完整的文件详情对象
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FileApprovalPreviewScreen(
          fileDetail: fileDetail,
          studentName: studentName,
        ),
      ),
    ).then((result) {
      // 如果审批状态发生变化，刷新列表
      if (result == true) {
        _loadData();
      }
    });
  }
}
