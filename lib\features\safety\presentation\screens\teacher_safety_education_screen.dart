import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/router/app_navigator.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/constants.dart';
import 'safety_exam_detail_screen.dart';
import '../widgets/safety_stat_card.dart';
import '../widgets/class_group_section.dart';
import '../bloc/teacher_safety_education/teacher_safety_education_bloc.dart';
import '../bloc/teacher_safety_education/teacher_safety_education_event.dart';
import '../bloc/teacher_safety_education/teacher_safety_education_state.dart';
import '../../domain/models/teacher_safety_education_response.dart';

/// 教师端安全教育试题页面
///
/// 展示学生安全教育试题的完成情况，包括考试统计和学生列表
/// 支持按班级分组查看学生的安全教育试题分数
///
/// <AUTHOR>
/// @date 2025-04-28
/// @version 1.0
class TeacherSafetyEducationScreen extends StatefulWidget {
  const TeacherSafetyEducationScreen({Key? key}) : super(key: key);

  @override
  State<TeacherSafetyEducationScreen> createState() => _TeacherSafetyEducationScreenState();
}

class _TeacherSafetyEducationScreenState extends State<TeacherSafetyEducationScreen> {
  late TeacherSafetyEducationBloc _bloc;
  late PlanListGlobalBloc _planListBloc;

  @override
  void initState() {
    super.initState();
    _bloc = GetIt.instance<TeacherSafetyEducationBloc>();
    _planListBloc = GetIt.instance<PlanListGlobalBloc>();

    // 监听实习计划变化
    _planListBloc.stream.listen((planState) {
      if (planState is PlanListGlobalLoadedState && planState.currentPlan != null) {
        _bloc.add(LoadTeacherSafetyEducationDataEvent(planId: planState.currentPlan!.planId));
      }
    });

    // 初始加载数据
    final currentPlanState = _planListBloc.state;
    if (currentPlanState is PlanListGlobalLoadedState && currentPlanState.currentPlan != null) {
      _bloc.add(LoadTeacherSafetyEducationDataEvent(planId: currentPlanState.currentPlan!.planId));
    }
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: CustomAppBar(
        title: '实习生安全教育试题',
        onBackPressed: () {
          AppNavigator.goToHome(context);
        },
      ),
      body: Column(
        children: [
          // 实习/学期信息 - 固定在顶部，自动从全局状态获取实习计划数据
          const CourseHeaderSection(),

          // 主要内容区域 - 可滚动
          Expanded(
            child: BlocBuilder<TeacherSafetyEducationBloc, TeacherSafetyEducationState>(
              bloc: _bloc,
              builder: (context, state) {
                return RefreshIndicator(
                  onRefresh: () async {
                    final planState = _planListBloc.state;
                    if (planState is PlanListGlobalLoadedState && planState.currentPlan != null) {
                      _bloc.add(RefreshTeacherSafetyEducationDataEvent(planId: planState.currentPlan!.planId));
                    }
                  },
                  child: _buildContent(context, state),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建页面内容
  Widget _buildContent(BuildContext context, TeacherSafetyEducationState state) {
    if (state is TeacherSafetyEducationLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (state is TeacherSafetyEducationError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              state.message,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                final planState = _planListBloc.state;
                if (planState is PlanListGlobalLoadedState && planState.currentPlan != null) {
                  _bloc.add(LoadTeacherSafetyEducationDataEvent(planId: planState.currentPlan!.planId));
                }
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (state is TeacherSafetyEducationEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '暂无安全教育考试数据',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    // 加载成功或刷新中状态
    if (state is TeacherSafetyEducationLoaded ||
        state is TeacherSafetyEducationRefreshing ||
        state is TeacherSafetyEducationRefreshError) {

      late final TeacherSafetyEducationResponse data;
      if (state is TeacherSafetyEducationLoaded) {
        data = state.data;
      } else if (state is TeacherSafetyEducationRefreshing) {
        data = state.previousData;
      } else if (state is TeacherSafetyEducationRefreshError) {
        data = state.previousData;
      }

      return ListView(
        padding: EdgeInsets.zero,
        children: [
          const SizedBox(height: 16),

          // 统计卡片区域
          Container(
            height: 138.h,
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Row(
              children: [
                Expanded(
                  child: SafetyStatCard(
                    icon: Icons.check_circle_outline,
                    label: '已考试',
                    numerator: data.statistics.alreadyExam,
                    denominator: data.statistics.shouldExam,
                    color: const Color(0xFF5DD1C3),
                  ),
                ),
                Expanded(
                  child: SafetyStatCard(
                    icon: Icons.chat_bubble_outline,
                    label: '已及格',
                    numerator: data.statistics.alreadyPass,
                    denominator: data.statistics.alreadyExam,
                    color: const Color(0xFF00C853),
                  ),
                ),
                Expanded(
                  child: SafetyStatCard(
                    icon: Icons.insert_chart_outlined,
                    label: '80分以上',
                    numerator: data.statistics.above,
                    denominator: data.statistics.alreadyExam,
                    color: const Color(0xFF4B7CFF),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // 班级分组与学生列表
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: data.studentList.map(_buildClassGroupSection).toList(),
            ),
          ),
          // 底部留白，确保滚动到底部时有足够空间
          const SizedBox(height: 16),
        ],
      );
    }

    // 默认返回空容器
    return Container();
  }

  /// 构建班级分组组件
  Widget _buildClassGroupSection(group) {
    // 转换数据格式以适配现有的ClassGroupSection组件
    final students = group.list.map((student) => {
      'name': student.studentName,
      'avatar': student.avatar ?? AppConstants.avatar1,
      'phone': student.phone,
      'score': student.score.toInt(),
      'studentId': student.studentId,
      'recordId': student.recordId,
    }).toList();

    return ClassGroupSection(
      className: group.name,
      students: students,
      onStudentTap: (studentData) {
        // 处理学生列表项点击事件
        _bloc.add(StudentItemClickedEvent(
          studentId: studentData['studentId'],
          studentName: studentData['name'],
          recordId: studentData['recordId'],
        ));

        // 跳转到学生考试详情页面
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => SafetyExamDetailScreen(
              studentId: studentData['studentId'],
              studentName: studentData['name'],
            ),
          ),
        );
      },
    );
  }
}
