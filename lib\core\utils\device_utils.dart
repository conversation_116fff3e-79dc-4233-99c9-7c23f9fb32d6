import 'package:shared_preferences/shared_preferences.dart';
import 'dart:math';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// 设备工具类
///
/// 提供设备相关的工具方法，如获取设备ID等
class DeviceUtils {
  static const String _deviceIdKey = 'device_id';

  /// 获取设备ID
  ///
  /// 首先尝试从设备信息获取唯一标识符
  /// 如果本地存储中已有设备ID，则返回该ID
  /// 否则生成一个新的设备ID并保存到本地存储
  static Future<String> getDeviceId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String? deviceId = prefs.getString(_deviceIdKey);

      if (deviceId == null || deviceId.isEmpty) {
        // 尝试从设备信息获取唯一标识符
        deviceId = await _getDeviceInfoId();
        debugPrint('从设备信息获取的设备ID: $deviceId');

        if (deviceId == null || deviceId.isEmpty) {
          // 如果无法从设备信息获取，则生成一个随机ID
          deviceId = _generateRandomDeviceId();
          debugPrint('生成的随机设备ID: $deviceId');
        }

        // 保存设备ID到本地存储
        await prefs.setString(_deviceIdKey, deviceId);
      } else {
        debugPrint('从本地存储获取的设备ID: $deviceId');
      }

      return deviceId;
    } catch (e) {
      debugPrint('获取设备ID失败: $e');
      // 如果无法访问本地存储或设备信息，返回一个临时ID
      final randomId = _generateRandomDeviceId();
      debugPrint('发生错误，使用随机设备ID: $randomId');
      return randomId;
    }
  }

  /// 从设备信息获取唯一标识符
  ///
  /// 根据不同平台获取不同的设备标识符
  static Future<String?> _getDeviceInfoId() async {
    final deviceInfoPlugin = DeviceInfoPlugin();
    try {
      if (kIsWeb) {
        // Web平台
        final webInfo = await deviceInfoPlugin.webBrowserInfo;
        debugPrint('Web设备信息: ${webInfo.userAgent}');
        return webInfo.userAgent ?? webInfo.browserName.name;
      } else if (Platform.isAndroid) {
        // Android平台
        final androidInfo = await deviceInfoPlugin.androidInfo;
        debugPrint('Android设备信息: ${androidInfo.id}');
        return androidInfo.id; // Android ID
      } else if (Platform.isIOS) {
        // iOS平台
        final iosInfo = await deviceInfoPlugin.iosInfo;
        debugPrint('iOS设备信息: ${iosInfo.identifierForVendor}');
        return iosInfo.identifierForVendor; // UUID for vendor
      } else if (Platform.isWindows) {
        // Windows平台
        final windowsInfo = await deviceInfoPlugin.windowsInfo;
        debugPrint('Windows设备信息: ${windowsInfo.deviceId}');
        return windowsInfo.deviceId;
      } else if (Platform.isMacOS) {
        // macOS平台
        final macOsInfo = await deviceInfoPlugin.macOsInfo;
        debugPrint('macOS设备信息: ${macOsInfo.systemGUID}');
        return macOsInfo.systemGUID;
      } else if (Platform.isLinux) {
        // Linux平台
        final linuxInfo = await deviceInfoPlugin.linuxInfo;
        debugPrint('Linux设备信息: ${linuxInfo.machineId}');
        return linuxInfo.machineId;
      }
    } catch (e) {
      debugPrint('获取设备信息失败: $e');
    }
    return null;
  }

  /// 生成随机设备ID
  ///
  /// 生成一个16位的随机字符串作为设备ID
  static String _generateRandomDeviceId() {
    const chars = '0123456789abcdef';
    final random = Random();
    final result = StringBuffer();

    for (var i = 0; i < 16; i++) {
      result.write(chars[random.nextInt(chars.length)]);
    }

    return result.toString();
  }
}
