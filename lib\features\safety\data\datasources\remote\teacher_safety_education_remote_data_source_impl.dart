/// -----
/// teacher_safety_education_remote_data_source_impl.dart
/// 
/// 教师端安全教育考试远程数据源实现
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/error/exceptions/server_exception.dart';

import '../../../../../core/network/dio_client.dart';
import '../../../../../core/utils/logger.dart';
import '../../models/teacher_safety_education_response_model.dart';
import 'teacher_safety_education_remote_data_source.dart';

/// 教师端安全教育考试远程数据源实现
/// 
/// 实现从远程API获取教师端安全教育考试数据的具体逻辑
class TeacherSafetyEducationRemoteDataSourceImpl implements TeacherSafetyEducationRemoteDataSource {
  final DioClient _dioClient;
  
  static const String _tag = 'TeacherSafetyEducationRemoteDataSource';

  TeacherSafetyEducationRemoteDataSourceImpl(this._dioClient);

  @override
  Future<TeacherSafetyEducationResponseModel> getTeacherSafetyEducationData(String planId) async {
    try {
      Logger.info(_tag, '开始获取教师端安全教育考试数据，planId: $planId');
      
      final response = await _dioClient.get(
        'internshipservice/v1/internship/teacher/examQuestion/list',
        queryParameters: {
          'planId': planId,
        },
      );

      Logger.debug(_tag, '获取教师端安全教育考试数据响应: $response');

      if (response is Map<String, dynamic>) {
        final responseModel = TeacherSafetyEducationResponseModel.fromJson(response);
        Logger.info(_tag, '成功获取教师端安全教育考试数据，学生分组数量: ${responseModel.studentList.length}');
        return responseModel;
      } else {
        Logger.warning(_tag, '响应数据格式不正确');
        throw ServerException('响应数据格式不正确');
      }
    } catch (e) {
      Logger.error(_tag, '获取教师端安全教育考试数据失败: $e');
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('获取教师端安全教育考试数据失败: $e');
    }
  }
}
