import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_demo/features/safety/domain/models/safety_exam.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../widgets/exam_question_item.dart';

/// 安全教育考试详情页面
///
/// 展示学生的安全教育考试情况，包括得分和每道题的答题情况
///
/// <AUTHOR>
/// @date 2025-04-30
/// @version 1.0
class SafetyExamDetailScreen extends StatefulWidget {
  /// 学生ID
  final String studentId;

  /// 学生姓名
  final String studentName;

  const SafetyExamDetailScreen({
    Key? key,
    required this.studentId,
    required this.studentName,
  }) : super(key: key);

  @override
  State<SafetyExamDetailScreen> createState() => _SafetyExamDetailScreenState();
}

class _SafetyExamDetailScreenState extends State<SafetyExamDetailScreen> {
  /// 考试数据
  SafetyExam? _examData;

  /// 加载状态
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadExamData();
  }

  /// 加载考试数据
  Future<void> _loadExamData() async {
    try {
      // 在实际应用中，这里应该从API获取数据，并传入学生ID
      // 这里为了演示，我们从本地JSON文件加载
      final String response = await rootBundle.loadString('assets/data/safety_exam_data.json');
      final data = await json.decode(response);

      setState(() {
        _examData = SafetyExam.fromJson(data);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // 错误处理
      print('加载考试数据失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Color(0xFF333333), size: 18),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          '安全教育考试情况',
          style: TextStyle(
            color: Color(0xFF333333),
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildExamContent(),
    );
  }

  /// 构建考试内容
  Widget _buildExamContent() {
    if (_examData == null) {
      return const Center(
        child: Text('暂无考试数据'),
      );
    }

    return Padding(
      padding: EdgeInsets.only(top: 30.h),
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: ListView(

          padding: EdgeInsets.symmetric(horizontal: 38.w),
          children: [
            // 考试得分
            Container(
              padding: EdgeInsets.symmetric(vertical: 44.h),
              child: Row(
                children: [
                  Text(
                    '考试得分',
                    style: TextStyle(
                      fontSize: 30.sp,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '(${_examData!.score}分)',
                    style: TextStyle(
                      fontSize: 30.sp,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF4B7CFF),
                    ),
                  ),
                ],
              ),
            ),

            // 试题列表
            ..._examData!.questions.map<Widget>((question) => ExamQuestionItem(
              question: question,
            )).toList(),
          ],
        ),
      ),
    );
  }
}
