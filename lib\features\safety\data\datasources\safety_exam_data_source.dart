/// -----
/// safety_exam_data_source.dart
/// 
/// 安全教育考试数据源
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';

/// 安全教育考试数据源接口
///
/// 定义获取安全教育考试题目的方法
abstract class SafetyExamDataSource {
  /// 获取安全教育考试题目列表
  ///
  /// [planId] 实习计划ID，可选参数
  Future<List<SafetyExamQuestion>> getExamQuestions({String? planId});
}

/// 本地安全教育考试数据源实现（已废弃，使用RemoteSafetyExamDataSource）
///
/// 从本地JSON数据中获取安全教育考试题目
@Deprecated('使用RemoteSafetyExamDataSource替代')
class LocalSafetyExamDataSource implements SafetyExamDataSource {
  @override
  Future<List<SafetyExamQuestion>> getExamQuestions({String? planId}) async {
    // 返回空列表，强制使用远程数据源
    return [];
  }
}


