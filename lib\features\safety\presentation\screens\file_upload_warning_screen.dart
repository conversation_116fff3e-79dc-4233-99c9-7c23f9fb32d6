/// -----
/// file_upload_warning_screen.dart
/// 
/// 文件未传预警页面，用于显示学生未上传文件的预警信息
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 文件类型枚举
enum FileType {
  /// 三方协议
  tripartiteAgreement,
  /// 告家长通知书
  parentNotification,
  /// 实习保险单
  internshipInsurance,
  /// 自助协议申请表
  selfServiceAgreement,
}

/// 文件信息模型
class FileInfo {
  final FileType type;
  final String name;

  const FileInfo({
    required this.type,
    required this.name,
  });
}

/// 文件上传预警记录模型
class FileUploadWarningRecord {
  final String studentName;
  final String phoneNumber;
  final List<FileInfo> missingFiles;
  final String avatarUrl;

  const FileUploadWarningRecord({
    required this.studentName,
    required this.phoneNumber,
    required this.missingFiles,
    this.avatarUrl = '',
  });
}

class FileUploadWarningScreen extends StatelessWidget {
  const FileUploadWarningScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 模拟数据
    final List<FileUploadWarningRecord> warningRecords = [
      const FileUploadWarningRecord(
        studentName: '李成儒',
        phoneNumber: '13569874562',
        avatarUrl: 'https://picsum.photos/88/88?random=1',
        missingFiles: [
          const FileInfo(type: FileType.tripartiteAgreement, name: '三方协议'),
          const FileInfo(type: FileType.parentNotification, name: '告家长通知书'),
          const FileInfo(type: FileType.internshipInsurance, name: '实习保险单'),
          const FileInfo(type: FileType.selfServiceAgreement, name: '自助协议申请表'),
        ],
      ),
      const FileUploadWarningRecord(
        studentName: '李成儒',
        phoneNumber: '13569874562',
        avatarUrl: 'https://picsum.photos/88/88?random=2',
        missingFiles: [
          const FileInfo(type: FileType.tripartiteAgreement, name: '三方协议'),
          const FileInfo(type: FileType.parentNotification, name: '告家长通知书'),
        ],
      ),
      const FileUploadWarningRecord(
        studentName: '李成儒',
        phoneNumber: '13569874562',
        avatarUrl: 'https://picsum.photos/88/88?random=3',
        missingFiles: [
          const FileInfo(type: FileType.tripartiteAgreement, name: '三方协议'),
          const FileInfo(type: FileType.parentNotification, name: '告家长通知书'),
          const FileInfo(type: FileType.internshipInsurance, name: '实习保险单'),
        ],
      ),
      const FileUploadWarningRecord(
        studentName: '李成儒',
        phoneNumber: '13569874562',
        avatarUrl: '',
        missingFiles: [
          const FileInfo(type: FileType.tripartiteAgreement, name: '三方协议'),
        ],
      ),
    ];

    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: const CustomAppBar(
        title: '文件未传预警',
        centerTitle: true,
        showBackButton: true,
      ),
      body: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
        itemCount: warningRecords.length,
        itemBuilder: (context, index) {
          return _buildFileUploadWarningItem(warningRecords[index]);
        },
      ),
    );
  }

  /// 构建文件上传预警列表项
  Widget _buildFileUploadWarningItem(FileUploadWarningRecord record) {
    return Container(
      margin: EdgeInsets.only(bottom: 20.h),
      padding: EdgeInsets.all(30.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧头像
          _buildAvatar(record.studentName, avatarUrl: record.avatarUrl),
          
          SizedBox(width: 24.w),
          
          // 中间信息
          Expanded(
            child: _buildStudentInfo(record),
          ),
        ],
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar(String studentName, {String? avatarUrl}) {
    return Container(
      width: 88.w,
      height: 88.w,
      decoration: const BoxDecoration(
        color: Color(0xFF2165F6),
        shape: BoxShape.circle,
      ),
      child: ClipOval(
        child: avatarUrl != null && avatarUrl.isNotEmpty
            ? Image.network(
                avatarUrl,
                width: 88.w,
                height: 88.w,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultAvatar(studentName);
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) {
                    return child;
                  }
                  return _buildDefaultAvatar(studentName);
                },
              )
            : _buildDefaultAvatar(studentName),
      ),
    );
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar(String studentName) {
    return Container(
      width: 88.w,
      height: 88.w,
      decoration: const BoxDecoration(
        color: Color(0xFF2165F6),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          studentName.isNotEmpty ? studentName.substring(0, 1) : '',
          style: TextStyle(
            fontSize: 32.sp,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// 构建学生信息
  Widget _buildStudentInfo(FileUploadWarningRecord record) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 姓名和电话
        Row(
          children: [
            Text(
              record.studentName,
              style: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF333333),
              ),
            ),
            SizedBox(width: 20.w),
            Icon(
              Icons.phone,
              size: 28.w,
              color: const Color(0xFF666666),
            ),
            SizedBox(width: 8.w),
            Text(
              record.phoneNumber,
              style: TextStyle(
                fontSize: 28.sp,
                color: const Color(0xFF666666),
              ),
            ),
          ],
        ),
        
        SizedBox(height: 16.h),
        
        // 未上传文件提示
        Text(
          '以下文件未上传：',
          style: TextStyle(
            fontSize: 28.sp,
            color: const Color(0xFF666666),
          ),
        ),
        
        SizedBox(height: 20.h),
        
        // 文件列表
        _buildFileGrid(record.missingFiles),
      ],
    );
  }

  /// 构建文件网格
  Widget _buildFileGrid(List<FileInfo> files) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 20.w,
        mainAxisSpacing: 20.h,
        childAspectRatio: 3,
      ),
      itemCount: files.length,
      itemBuilder: (context, index) {
        return _buildFileItem(files[index]);
      },
    );
  }

  /// 构建文件项
  Widget _buildFileItem(FileInfo file) {
    return Row(
      children: [
        // PDF图标
        Container(
          width: 60.w,
          height: 60.w,
          decoration: BoxDecoration(
            color: const Color(0xFF2165F6),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Center(
            child: Text(
              'PDF',
              style: TextStyle(
                fontSize: 20.sp,
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),

        SizedBox(width: 18.w),

        // 文件名
        Expanded(
          child: Text(
            file.name,
            style: TextStyle(
              fontSize: 24.sp,
              color: const Color(0xFF333333),
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
