/// -----
/// remote_safety_exam_data_source.dart
/// 
/// 远程安全教育考试数据源
///
/// <AUTHOR>
/// @date 2025-06-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';
import 'package:flutter_demo/features/safety/data/datasources/safety_exam_data_source.dart';

/// 远程安全教育考试数据源实现
///
/// 从远程API获取安全教育考试题目
class RemoteSafetyExamDataSource implements SafetyExamDataSource {
  /// 网络客户端
  final DioClient dioClient;

  /// 构造函数
  RemoteSafetyExamDataSource({
    required this.dioClient,
  });

  @override
  Future<List<SafetyExamQuestion>> getExamQuestions({String? planId}) async {
    try {
      // 构建请求参数
      final Map<String, dynamic> queryParams = {};
      if (planId != null) {
        queryParams['planId'] = planId;
      }

      // 发送GET请求
      final response = await dioClient.get(
        'internshipservice/v1/internship/student/examQuestion/list',
        queryParameters: queryParams,
      );

      // 解析响应数据
      // DioClient的_extractData方法已经提取了data字段，所以直接使用response即可
      List<dynamic> data;

      if (response is List) {
        // 如果response直接是List，说明DioClient已经提取了data字段
        data = response;
      } else if (response is Map && response.containsKey('data')) {
        // 如果response是Map且包含data字段，提取data
        data = response['data'] as List<dynamic>;
      } else {
        throw Exception('API响应格式不正确: 期望List或包含data字段的Map，实际收到: ${response.runtimeType}');
      }

      // 转换为SafetyExamQuestion对象列表
      final List<SafetyExamQuestion> questions = [];
      for (int i = 0; i < data.length; i++) {
        try {
          final questionData = data[i] as Map<String, dynamic>;
          final question = SafetyExamQuestion.fromJson(questionData, i + 1);
          questions.add(question);
        } catch (e) {
          throw Exception('解析题目 ${i + 1} 失败: $e，题目数据: ${data[i]}');
        }
      }

      return questions;
    } catch (e) {
      throw Exception('获取考试题目失败: ${e.toString()}');
    }
  }
}
