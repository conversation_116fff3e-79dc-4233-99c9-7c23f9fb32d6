/// -----
/// student_list_item.dart
/// 
/// 可复用的学生列表项组件，支持不同的显示模式和操作
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_demo/core/widgets/status_tag.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';

/// 学生列表项显示模式
enum StudentListItemMode {
  /// 报告统计模式（显示姓名+状态标签在同一行）
  report,
  /// 签到统计模式（显示姓名和状态标签分开）
  checkin,
}

/// 学生基础信息接口
abstract class StudentInfo {
  String get id;
  String get name;
  String get phone;
  String get avatar;
  String get status;
}

/// 可复用的学生列表项组件
/// 
/// 支持不同的显示模式和操作按钮
/// 可以用于报告统计、签到统计等不同场景
class StudentListItem extends StatelessWidget {
  /// 学生信息
  final StudentInfo student;
  
  /// 显示模式
  final StudentListItemMode mode;
  
  /// 操作按钮文本
  final String? actionButtonText;
  
  /// 操作按钮点击回调
  final VoidCallback? onActionButtonTap;
  
  /// 是否显示操作按钮
  final bool showActionButton;
  
  /// 自定义内边距
  final EdgeInsetsGeometry? padding;
  
  /// 自定义外边距
  final EdgeInsetsGeometry? margin;

  const StudentListItem({
    Key? key,
    required this.student,
    this.mode = StudentListItemMode.report,
    this.actionButtonText,
    this.onActionButtonTap,
    this.showActionButton = true,
    this.padding,
    this.margin,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin ?? EdgeInsets.only(bottom: 20.h),
      padding: padding ?? EdgeInsets.all(32.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 头像
          CircleAvatar(
            radius: 40.r,
            backgroundImage: NetworkImage(student.avatar),
            backgroundColor: AppTheme.blue2165f6.withValues(alpha: 0.1),
          ),
          SizedBox(width: 24.w),
          // 学生信息
          Expanded(
            child: _buildStudentInfo(),
          ),
          if (showActionButton && actionButtonText != null) ...[
            SizedBox(width: 16.w),
            // 操作按钮
            _buildActionButton(),
          ],
        ],
      ),
    );
  }

  /// 构建学生信息区域
  Widget _buildStudentInfo() {
    return _buildReportModeInfo();
  }

  /// 构建报告模式的学生信息（姓名和状态标签在同一行）
  Widget _buildReportModeInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 姓名和状态标签在同一行
        RichText(
          text: TextSpan(
            children: [
              // 姓名
              TextSpan(
                text: student.name,
                style: TextStyle(
                  fontSize: 28.sp,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.black333,
                ),
              ),
              // 间距
              WidgetSpan(
                child: SizedBox(width: 10.w),
              ),
              // 状态标签
              WidgetSpan(
                child: StatusTag(
                  text: student.status,
                  type: _getStatusTagType(student.status),
                  padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 3.h),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 8.h),
        // 电话
        _buildPhoneInfo(),
      ],
    );
  }


  /// 构建电话信息
  Widget _buildPhoneInfo() {
    return GestureDetector(
      onTap: () => _makePhoneCall(student.phone),
      child: Row(
        children: [
          Icon(
            Icons.phone,
            size: 32.w,
            color: AppTheme.black666,
          ),
          SizedBox(width: 12.w),
          Text(
            student.phone,
            style: TextStyle(
              fontSize: 24.sp,
              color: AppTheme.black666,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton() {
    return GestureDetector(
      onTap: onActionButtonTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 10.h),
        decoration: BoxDecoration(
          border: Border.all(color: AppTheme.primaryColor),
          borderRadius: BorderRadius.circular(10.r),
        ),
        child: Text(
          actionButtonText!,
          style: TextStyle(
            fontSize: 26.sp,
            color: AppTheme.primaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// 获取状态标签类型
  StatusTagType _getStatusTagType(String status) {
    switch (status) {
      case '待实习':
        return StatusTagType.pending;
      case '实习中':
        return StatusTagType.ongoing;
      case '未认证':
        return StatusTagType.unverified;
      case '已认证':
        return StatusTagType.verified;
      default:
        return StatusTagType.pending;
    }
  }

  /// 拨打电话
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    try {
      if (await canLaunchUrl(launchUri)) {
        await launchUrl(launchUri);
      }
    } on Exception catch (e) {
      // 处理错误
      debugPrint('无法拨打电话: $phoneNumber, 错误: $e');
    }
  }
}
