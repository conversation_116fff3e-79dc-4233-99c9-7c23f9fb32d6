import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

/// 学生安全教育试题列表项组件
///
/// 用于展示单个学生的安全教育试题信息，包括姓名、头像、分数等
/// 支持根据分数显示不同颜色，并提供电话联系功能
///
/// <AUTHOR>
/// @date 2025-04-28
/// @version 1.0
class StudentSafetyListItem extends StatelessWidget {
  /// 学生信息数据
  final Map<String, dynamic> student;

  const StudentSafetyListItem({Key? key, required this.student}) : super(key: key);

  /// 拨打电话
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    } else {
      throw '无法拨打电话: $phoneNumber';
    }
  }

  @override
  Widget build(BuildContext context) {
    // 根据分数设置不同的颜色
    Color scoreColor;
    if (student['score'] >= 80) {
      scoreColor = const Color(0xFF4B7CFF); // 蓝色，80分以上
    } else if (student['score'] >= 60) {
      scoreColor = const Color(0xFF4ECB73); // 绿色，及格
    } else {
      scoreColor = const Color(0xFFFF5252); // 红色，不及格
    }

    return InkWell(
      onTap: () {
        // TODO: 实现安全教育考试详情页面导航
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('安全教育考试详情功能开发中')),
        );
      },
      child: Container(
        // height: 160.h,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 头像
                CircleAvatar(
                  backgroundImage: NetworkImage(student['avatar']),
                  radius: 20,
                ),
                const SizedBox(width: 12),

                // 学生姓名
                Text(
                  student['name'],
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 15,
                    color: Color(0xFF333333),
                  ),
                ),

                const Spacer(),

                // 电话图标和号码组合
                GestureDetector(
                  onTap: () {
                    final String phoneNumber = student['phone'] ?? '13569874562';
                    _makePhoneCall(phoneNumber);
                  },
                  child: Row(
                    children: [
                      // 电话图标（圆形白色背景）
                      Container(
                        width: 30,
                        height: 30,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          border: Border.all(color: const Color(0xFFEEEEEE), width: 1),
                        ),
                        child: const Icon(
                          Icons.phone,
                          size: 14,
                          color: Color(0xFF999999),
                        ),
                      ),

                      const SizedBox(width: 8),

                      // 电话号码
                      Text(
                        student['phone'] ?? '13569874562',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF999999),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 12),

                // 右箭头
                const Icon(
                  Icons.chevron_right,
                  color: Color(0xFFCCCCCC),
                  size: 16,
                ),
              ],
            ),
            const SizedBox(height: 27),
            Row(

              children: [
                // 安全教育试题文本和分数
                const Text(
                  '安全教育试题：',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF666666),
                  ),
                ),
                Text(
                  '${student['score']}分',
                  style: TextStyle(
                    color: scoreColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),


              ],
            )
          ],
        ),
      ),
    );
  }
}
